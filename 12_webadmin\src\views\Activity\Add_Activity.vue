<!--活动添加/编辑页面-->
<template>
  <div class="grid-content add-activity">
    <div class="header">
      <div class="header-line1">
        <div class="title">
          {{ isEdit ? '编辑活动' : '添加活动' }}
        </div>
        <div class="actions">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">
            {{ isEdit ? '更新' : '保存' }}
          </el-button>
        </div>
      </div>
    </div>

    <div class="content">
      <el-card class="form-card">
        <div slot="header" class="card-header">
          <span>基本信息</span>
        </div>
        
        <el-form
          ref="form"
          :model="formModel"
          :rules="formRules"
          label-width="120px"
          class="activity-form">
          
          <el-form-item label="活动名称" prop="ActiveName">
            <el-input
              v-model="formModel.ActiveName"
              :placeholder="columnOptions.ActiveName.placeholder"
              :maxlength="columnOptions.ActiveName.maxLength"
              show-word-limit
              clearable>
            </el-input>
          </el-form-item>

          <el-form-item label="活动描述">
            <el-input
              v-model="formModel.Description"
              type="textarea"
              :rows="4"
              placeholder="请输入活动描述"
              maxlength="500"
              show-word-limit>
            </el-input>
          </el-form-item>

          <el-form-item label="活动时间">
            <el-date-picker
              v-model="activityDateRange"
              type="datetimerange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>

          <el-form-item label="活动地点">
            <el-input
              v-model="formModel.Location"
              placeholder="请输入活动地点"
              clearable>
            </el-input>
          </el-form-item>

          <el-form-item label="参与人数限制">
            <el-input-number
              v-model="formModel.MaxParticipants"
              :min="0"
              :max="10000"
              placeholder="0表示不限制">
            </el-input-number>
            <span class="form-tip">设置为0表示不限制参与人数</span>
          </el-form-item>

          <el-form-item label="活动状态">
            <el-radio-group v-model="formModel.Status">
              <el-radio :label="1">草稿</el-radio>
              <el-radio :label="2">发布</el-radio>
              <el-radio :label="3">结束</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="是否需要报名">
            <el-switch
              v-model="formModel.RequireRegistration"
              active-text="是"
              inactive-text="否">
            </el-switch>
          </el-form-item>

        </el-form>
      </el-card>

      <!-- 二维码关联 -->
      <el-card class="form-card" style="margin-top: 20px;">
        <div slot="header" class="card-header">
          <span>二维码关联</span>
          <el-button type="text" @click="showQRCodeDialog">添加二维码</el-button>
        </div>
        
        <div class="qr-list">
          <div v-if="relatedQRCodes.length === 0" class="empty-state">
            <i class="el-icon-qrcode"></i>
            <p>暂无关联二维码</p>
          </div>
          <div v-else>
            <el-table :data="relatedQRCodes" style="width: 100%;">
              <el-table-column prop="qrName" label="二维码名称" />
              <el-table-column prop="qrType" label="二维码类型" width="120">
                <template slot-scope="scope">
                  <el-tag :type="getQRTypeColor(scope.row.qrType)">
                    {{ getQRTypeText(scope.row.qrType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="scanCount" label="扫描次数" width="100" />
              <el-table-column prop="createTime" label="关联时间" width="180" />
              <el-table-column label="操作" width="150">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="previewQRCode(scope.row)">
                    预览
                  </el-button>
                  <el-button type="text" size="small" @click="removeQRCode(scope.$index)">
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 问卷关联 -->
      <el-card class="form-card" style="margin-top: 20px;">
        <div slot="header" class="card-header">
          <span>问卷关联</span>
          <el-button type="text" @click="showSurveyDialog">添加问卷</el-button>
        </div>
        
        <div class="survey-list">
          <div v-if="relatedSurveys.length === 0" class="empty-state">
            <i class="el-icon-document"></i>
            <p>暂无关联问卷</p>
          </div>
          <div v-else>
            <el-table :data="relatedSurveys" style="width: 100%;">
              <el-table-column prop="surveyName" label="问卷名称" />
              <el-table-column prop="surveyType" label="问卷类型" width="120" />
              <el-table-column prop="createTime" label="关联时间" width="180" />
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="removeSurvey(scope.$index)">
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 二维码选择对话框 -->
    <el-dialog
      :visible.sync="qrCodeDialogVisible"
      title="选择二维码"
      width="900px">
      <div class="qr-selector">
        <div class="search-bar">
          <el-input
            v-model="qrSearchKeyword"
            placeholder="搜索二维码名称或类型"
            prefix-icon="el-icon-search"
            clearable
            @input="searchQRCodes">
          </el-input>
          <el-select v-model="qrTypeFilter" placeholder="二维码类型" clearable style="margin-left: 10px; width: 150px;">
            <el-option label="全部" value=""></el-option>
            <el-option label="活动推广" value="promotion"></el-option>
            <el-option label="报名入口" value="registration"></el-option>
            <el-option label="签到二维码" value="checkin"></el-option>
            <el-option label="分享二维码" value="share"></el-option>
          </el-select>
        </div>
        <el-table
          :data="filteredQRCodes"
          style="width: 100%; margin-top: 15px;"
          @selection-change="handleQRCodeSelection"
          max-height="400">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="qrName" label="二维码名称" />
          <el-table-column prop="qrType" label="类型" width="120">
            <template slot-scope="scope">
              <el-tag :type="getQRTypeColor(scope.row.qrType)">
                {{ getQRTypeText(scope.row.qrType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="scanCount" label="扫描次数" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="预览" width="80">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="previewQRCode(scope.row)">
                预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="qrCodeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmQRCodeSelection">确定</el-button>
      </div>
    </el-dialog>

    <!-- 二维码预览对话框 -->
    <el-dialog
      :visible.sync="qrPreviewVisible"
      title="二维码预览"
      width="400px"
      custom-class="qr-preview-dialog">
      <div class="qr-preview-content" v-if="previewQRCodeData">
        <div class="qr-info">
          <h4>{{ previewQRCodeData.qrName }}</h4>
          <div class="qr-image">
            <div class="qr-placeholder">
              <div class="qr-mock-preview">{{ previewQRCodeData.qrName }}</div>
            </div>
          </div>
          <div class="qr-details">
            <p><strong>类型：</strong>{{ getQRTypeText(previewQRCodeData.qrType) }}</p>
            <p><strong>扫描次数：</strong>{{ previewQRCodeData.scanCount }}</p>
            <p><strong>创建时间：</strong>{{ previewQRCodeData.createTime }}</p>
          </div>
        </div>
        <div class="qr-actions">
          <el-button type="primary" @click="copyQRCodeUrl(previewQRCodeData)">
            复制链接
          </el-button>
          <el-button @click="downloadQRCode(previewQRCodeData)">
            下载二维码
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 问卷选择对话框 -->
    <el-dialog
      :visible.sync="surveyDialogVisible"
      title="选择问卷"
      width="800px">
      <div class="survey-selector">
        <div class="search-bar">
          <el-input
            v-model="surveySearchKeyword"
            placeholder="搜索问卷"
            prefix-icon="el-icon-search"
            clearable>
          </el-input>
        </div>
        <el-table
          :data="availableSurveys"
          style="width: 100%; margin-top: 15px;"
          @selection-change="handleSurveySelection">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="SurveyName" label="问卷名称" />
          <el-table-column prop="BeginTime" label="开始时间" width="180" />
          <el-table-column prop="EndTime" label="结束时间" width="180" />
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.IsOnline ? 'success' : 'info'">
                {{ scope.row.IsOnline ? '已发布' : '未发布' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="surveyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSurveySelection">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import controller from '@/controllers/Activity/Add_Activity'
export default controller
</script>

<style scoped lang="scss">
.grid-content {
  margin: 0 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  height: 84px;
  background: #FFFFFF;
  border-radius: 6px;
  padding: 20px 30px;
  
  .header-line1 {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
    
    .actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }
}

.content {
  flex: 1;
  margin-top: 20px;
  overflow-y: auto;
  
  .form-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      span {
        font-weight: 600;
        color: #333;
      }
    }
  }
  
  .activity-form {
    .form-tip {
      margin-left: 10px;
      color: #999;
      font-size: 12px;
    }
  }
  
  .qr-list {
    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: #999;
      
      i {
        font-size: 48px;
        display: block;
        margin-bottom: 15px;
        color: #ccc;
      }
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
  
  .survey-list {
    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: #999;
      
      i {
        font-size: 48px;
        display: block;
        margin-bottom: 15px;
      }
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

.qr-selector {
  .search-bar {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    .el-input {
      flex: 1;
    }
  }
}

.survey-selector {
  .search-bar {
    margin-bottom: 15px;
  }
}

.dialog-footer {
  text-align: right;
}

.qr-preview-dialog {
  .el-dialog__body {
    text-align: center;
    
    .qr-preview-content {
      .qr-info {
        margin-bottom: 20px;
        
        h4 {
          margin-bottom: 15px;
          color: #333;
        }
        
        .qr-image {
          margin: 20px 0;
          
          .qr-placeholder {
            .qr-mock-preview {
              width: 200px;
              height: 200px;
              border: 2px dashed #ddd;
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto;
              color: #999;
              font-size: 14px;
              border-radius: 6px;
              background: #fafafa;
            }
          }
        }
        
        .qr-details {
          text-align: left;
          background: #f9f9f9;
          padding: 15px;
          border-radius: 6px;
          margin-top: 15px;
          
          p {
            margin: 8px 0;
            font-size: 14px;
            color: #666;
          }
        }
      }
      
      .qr-actions {
        display: flex;
        justify-content: center;
        gap: 10px;
      }
    }
  }
}
</style> 