<template>
  <div class="recent-activities">
    <div class="activity-header">
      <h3>最近活动</h3>
      <el-button type="text" @click="viewAll">查看全部</el-button>
    </div>
    
    <div class="activity-list" v-loading="loading">
      <div 
        v-for="activity in activities" 
        :key="activity.id" 
        class="activity-item"
        @click="viewDetail(activity)"
      >
        <div class="activity-icon">
          <i :class="getActivityIcon(activity.type)" :style="{ color: getActivityColor(activity.type) }"></i>
        </div>
        <div class="activity-content">
          <div class="activity-title">{{ activity.title }}</div>
          <div class="activity-desc">{{ activity.description }}</div>
          <div class="activity-time">{{ formatTime(activity.createTime) }}</div>
        </div>
        <div class="activity-status">
          <el-tag :type="getStatusType(activity.status)" size="mini">
            {{ getStatusText(activity.status) }}
          </el-tag>
        </div>
      </div>
      
      <div v-if="activities.length === 0" class="no-data">
        <img src="@/assets/images/no-data.png" alt="暂无数据" class="no-data-img">
        <p>暂无活动数据</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RecentActivities',
  props: {
    activities: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getActivityIcon(type) {
      const iconMap = {
        'user_register': 'el-icon-user-solid',
        'qr_scan': 'el-icon-camera-solid',
        'survey_submit': 'el-icon-document',
        'activity_join': 'el-icon-star-on',
        'default': 'el-icon-info'
      }
      return iconMap[type] || iconMap.default
    },
    
    getActivityColor(type) {
      const colorMap = {
        'user_register': '#67C23A',
        'qr_scan': '#E6A23C',
        'survey_submit': '#409EFF',
        'activity_join': '#F56C6C',
        'default': '#909399'
      }
      return colorMap[type] || colorMap.default
    },
    
    getStatusType(status) {
      const typeMap = {
        'active': 'success',
        'pending': 'warning',
        'completed': 'info',
        'cancelled': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        'active': '进行中',
        'pending': '待处理',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return textMap[status] || '未知'
    },
    
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else {
        return Math.floor(diff / 86400000) + '天前'
      }
    },
    
    viewDetail(activity) {
      this.$emit('view-detail', activity)
    },
    
    viewAll() {
      this.$emit('view-all')
    }
  }
}
</script>

<style lang="scss" scoped>
.recent-activities {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .activity-list {
    max-height: 400px;
    overflow-y: auto;
    
    .activity-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        i {
          font-size: 18px;
        }
      }
      
      .activity-content {
        flex: 1;
        
        .activity-title {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .activity-desc {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .activity-time {
          font-size: 12px;
          color: #C0C4CC;
        }
      }
      
      .activity-status {
        margin-left: 12px;
      }
    }
    
    .no-data {
      text-align: center;
      padding: 40px 0;
      color: #909399;
      
      .no-data-img {
        width: 60px;
        height: 60px;
        margin-bottom: 16px;
      }
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}
</style>
