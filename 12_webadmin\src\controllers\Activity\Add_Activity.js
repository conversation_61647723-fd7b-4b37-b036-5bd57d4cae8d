import * as apis from '@/apis/Activity/Active_Info'
import * as surveyApis from '@/apis/Survey/Survey_Info'
import * as models from '@/models/Activity/Active_Info'
import * as utils from '@/utils'

export default {
  name: 'AddActivity',
  
  data() {
    return {
      // 表单数据
      formModel: {
        ...models.formModel(),
        Description: '',
        Location: '',
        MaxParticipants: 0,
        Status: 1,
        RequireRegistration: false,
        StartTime: null,
        EndTime: null
      },
      formRules: models.formModelRules(),
      columnOptions: models.columnOptions(),
      
      // 页面状态
      isEdit: false,
      saveLoading: false,
      activityId: null,
      
      // 日期时间范围
      activityDateRange: [],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const start = new Date()
            const end = new Date()
            end.setTime(start.getTime() + 24 * 60 * 60 * 1000)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '明天',
          onClick(picker) {
            const start = new Date()
            start.setTime(start.getTime() + 24 * 60 * 60 * 1000)
            const end = new Date()
            end.setTime(start.getTime() + 24 * 60 * 60 * 1000)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '一周后',
          onClick(picker) {
            const start = new Date()
            start.setTime(start.getTime() + 7 * 24 * 60 * 60 * 1000)
            const end = new Date()
            end.setTime(start.getTime() + 24 * 60 * 60 * 1000)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      
      // 二维码关联相关
      relatedQRCodes: [],
      qrCodeDialogVisible: false,
      availableQRCodes: [],
      selectedQRCodes: [],
      qrSearchKeyword: '',
      qrTypeFilter: '',
      qrPreviewVisible: false,
      previewQRCodeData: null,
      
      // 问卷关联相关
      relatedSurveys: [],
      surveyDialogVisible: false,
      availableSurveys: [],
      selectedSurveys: [],
      surveySearchKeyword: ''
    }
  },
  
  computed: {
    // 过滤后的二维码列表
    filteredQRCodes() {
      let filtered = this.availableQRCodes
      
      // 按类型筛选
      if (this.qrTypeFilter) {
        filtered = filtered.filter(qr => qr.qrType === this.qrTypeFilter)
      }
      
      // 按关键词搜索
      if (this.qrSearchKeyword) {
        const keyword = this.qrSearchKeyword.toLowerCase()
        filtered = filtered.filter(qr => 
          qr.qrName.toLowerCase().includes(keyword) ||
          (qr.qrDescription && qr.qrDescription.toLowerCase().includes(keyword))
        )
      }
      
      return filtered
    },
    
    filteredSurveys() {
      if (!this.surveySearchKeyword) {
        return this.availableSurveys
      }
      return this.availableSurveys.filter(survey => 
        survey.SurveyName.toLowerCase().includes(this.surveySearchKeyword.toLowerCase())
      )
    }
  },
  
  created() {
    this.activityId = this.$route.query.id
    this.isEdit = !!this.activityId
    
    if (this.isEdit) {
      this.loadActivityData()
    }
    
    this.loadAvailableSurveys()
    this.loadAvailableQRCodes()
  },
  
  methods: {
    ...utils,
    
    // 加载活动数据（编辑模式）
    async loadActivityData() {
      try {
        const response = await apis.getById(this.activityId)
        if (response.state === 1 && response.data) {
          this.formModel = { ...this.formModel, ...response.data }
          
          // 设置日期范围
          if (response.data.StartTime && response.data.EndTime) {
            this.activityDateRange = [response.data.StartTime, response.data.EndTime]
          }
          
          // 加载关联的问卷
          this.loadRelatedSurveys()
        }
      } catch (error) {
        this.$message({
          type: 'error',
          message: '加载活动数据失败'
        })
      }
    },
    
    // 加载可用的问卷列表
    async loadAvailableSurveys() {
      try {
        const response = await surveyApis.fetchSelect({})
        if (response.state === 1) {
          this.availableSurveys = response.data || []
        }
      } catch (error) {
        console.error('加载问卷列表失败:', error)
      }
    },
    
    // 加载可用的二维码列表
    async loadAvailableQRCodes() {
      try {
        // 这里应该调用二维码API获取可用的二维码列表
        // 暂时使用模拟数据
        this.availableQRCodes = [
          {
            id: 1,
            qrName: '活动推广二维码',
            qrType: 'promotion',
            qrDescription: '用于活动推广的二维码',
            scanCount: 156,
            createTime: '2023-12-01 10:00:00',
            qrUrl: 'https://example.com/qr/promotion/1'
          },
          {
            id: 2,
            qrName: '报名入口二维码',
            qrType: 'registration',
            qrDescription: '用户报名入口',
            scanCount: 89,
            createTime: '2023-12-02 14:30:00',
            qrUrl: 'https://example.com/qr/registration/2'
          },
          {
            id: 3,
            qrName: '活动签到二维码',
            qrType: 'checkin',
            qrDescription: '现场签到使用',
            scanCount: 234,
            createTime: '2023-12-03 09:15:00',
            qrUrl: 'https://example.com/qr/checkin/3'
          },
          {
            id: 4,
            qrName: '分享邀请二维码',
            qrType: 'share',
            qrDescription: '邀请好友参与',
            scanCount: 78,
            createTime: '2023-12-04 16:45:00',
            qrUrl: 'https://example.com/qr/share/4'
          }
        ]
      } catch (error) {
        console.error('加载二维码列表失败:', error)
      }
    },

    // 加载已关联的问卷
    loadRelatedSurveys() {
      // 这里应该调用API获取已关联的问卷
      // 暂时使用模拟数据
      if (this.isEdit) {
        this.relatedSurveys = [
          {
            id: 1,
            surveyName: '活动满意度调查',
            surveyType: '满意度',
            createTime: '2023-12-01 10:00:00'
          }
        ]
      }
    },

    // 加载已关联的二维码
    loadRelatedQRCodes() {
      // 这里应该调用API获取已关联的二维码
      // 暂时使用模拟数据
      if (this.isEdit) {
        this.relatedQRCodes = [
          {
            id: 1,
            qrName: '活动推广二维码',
            qrType: 'promotion',
            scanCount: 156,
            createTime: '2023-12-01 15:30:00',
            qrUrl: 'https://example.com/qr/promotion/1'
          }
        ]
      }
    },
    
    // 保存数据
    async handleSave() {
      try {
        // 表单验证
        await this.$refs.form.validate()
        
        // 处理日期时间
        if (this.activityDateRange && this.activityDateRange.length === 2) {
          this.formModel.StartTime = this.activityDateRange[0]
          this.formModel.EndTime = this.activityDateRange[1]
        }
        
        this.saveLoading = true
        
        let response
        if (this.isEdit) {
          response = await apis.updateModel(this.formModel)
        } else {
          response = await apis.createModel(this.formModel)
        }
        
        if (response.state === 1) {
          this.$message({
            type: 'success',
            message: this.isEdit ? '更新成功' : '保存成功'
          })
          
          // 如果是新增，跳转到编辑页面继续配置
          if (!this.isEdit && response.data && response.data.Id) {
            this.$router.replace({
              name: 'AddActivity',
              query: { id: response.data.Id }
            })
            this.activityId = response.data.Id
            this.isEdit = true
          }
          
          // 保存问卷关联
          await this.saveRelatedSurveys()
          
        } else {
          this.$message({
            type: 'error',
            message: response.msg || '保存失败'
          })
        }
      } catch (error) {
        this.$message({
          type: 'error',
          message: '表单验证失败，请检查输入信息'
        })
      } finally {
        this.saveLoading = false
      }
    },
    
    // 保存问卷关联关系
    async saveRelatedSurveys() {
      if (!this.activityId) return
      
      // 这里应该调用API保存问卷关联关系
      // 暂时省略实际API调用
      console.log('保存问卷关联:', this.relatedSurveys)
    },
    
    // 返回列表页
    goBack() {
      this.$router.push({ name: 'ActiveInfoList' })
    },
    
    // 显示二维码选择对话框
    showQRCodeDialog() {
      this.qrCodeDialogVisible = true
      this.selectedQRCodes = []
      this.qrSearchKeyword = ''
      this.qrTypeFilter = ''
    },

    // 搜索二维码
    searchQRCodes() {
      // 搜索功能由computed属性filteredQRCodes自动处理
    },

    // 处理二维码选择
    handleQRCodeSelection(selection) {
      this.selectedQRCodes = selection
    },

    // 确认二维码选择
    confirmQRCodeSelection() {
      if (this.selectedQRCodes.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要关联的二维码'
        })
        return
      }

      this.selectedQRCodes.forEach(qrCode => {
        // 检查是否已经关联
        const exists = this.relatedQRCodes.find(item => item.id === qrCode.id)
        if (!exists) {
          this.relatedQRCodes.push({
            id: qrCode.id,
            qrName: qrCode.qrName,
            qrType: qrCode.qrType,
            scanCount: qrCode.scanCount,
            createTime: new Date().toLocaleString(),
            qrUrl: qrCode.qrUrl
          })
        }
      })

      this.qrCodeDialogVisible = false
      this.$message({
        type: 'success',
        message: `成功添加 ${this.selectedQRCodes.length} 个二维码关联`
      })
    },

    // 移除二维码关联
    removeQRCode(index) {
      this.$confirm('确定移除该二维码关联吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.relatedQRCodes.splice(index, 1)
        this.$message({
          type: 'success',
          message: '移除成功'
        })
      })
    },

    // 预览二维码
    previewQRCode(qrCode) {
      this.previewQRCodeData = qrCode
      this.qrPreviewVisible = true
    },

    // 获取二维码类型文本
    getQRTypeText(type) {
      const typeMap = {
        'promotion': '活动推广',
        'registration': '报名入口',
        'checkin': '签到二维码',
        'share': '分享二维码'
      }
      return typeMap[type] || type
    },

    // 获取二维码类型颜色
    getQRTypeColor(type) {
      const colorMap = {
        'promotion': 'success',
        'registration': 'primary',
        'checkin': 'warning',
        'share': 'info'
      }
      return colorMap[type] || 'default'
    },

    // 复制二维码链接
    copyQRCodeUrl(qrCode) {
      // 复制到剪贴板
      navigator.clipboard.writeText(qrCode.qrUrl).then(() => {
        this.$message({
          type: 'success',
          message: '链接已复制到剪贴板'
        })
      }).catch(() => {
        this.$message({
          type: 'error',
          message: '复制失败'
        })
      })
    },

    // 下载二维码
    downloadQRCode(qrCode) {
      // 这里实现二维码下载功能
      this.$message({
        type: 'info',
        message: '下载功能开发中...'
      })
    },

    // 显示问卷选择对话框
    showSurveyDialog() {
      this.surveyDialogVisible = true
      this.selectedSurveys = []
    },
    
    // 处理问卷选择
    handleSurveySelection(selection) {
      this.selectedSurveys = selection
    },
    
    // 确认问卷选择
    confirmSurveySelection() {
      this.selectedSurveys.forEach(survey => {
        // 检查是否已经关联
        const exists = this.relatedSurveys.find(item => item.id === survey.SurveyId)
        if (!exists) {
          this.relatedSurveys.push({
            id: survey.SurveyId,
            surveyName: survey.SurveyName,
            surveyType: '调查问卷',
            createTime: new Date().toLocaleString()
          })
        }
      })
      
      this.surveyDialogVisible = false
      this.$message({
        type: 'success',
        message: `成功添加 ${this.selectedSurveys.length} 个问卷关联`
      })
    },
    
    // 移除问卷关联
    removeSurvey(index) {
      this.$confirm('确定移除该问卷关联吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.relatedSurveys.splice(index, 1)
        this.$message({
          type: 'success',
          message: '移除成功'
        })
      })
    },
    
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields()
      this.formModel = {
        ...models.formModel(),
        Description: '',
        Location: '',
        MaxParticipants: 0,
        Status: 1,
        RequireRegistration: false,
        StartTime: null,
        EndTime: null
      }
      this.activityDateRange = []
      this.relatedSurveys = []
    }
  },
  
  watch: {
    // 监听日期范围变化
    activityDateRange(newVal) {
      if (newVal && newVal.length === 2) {
        this.formModel.StartTime = newVal[0]
        this.formModel.EndTime = newVal[1]
      } else {
        this.formModel.StartTime = null
        this.formModel.EndTime = null
      }
    }
  }
} 