<template>
  <div class="dashboard-editor-container">
    <!-- 用户统计面板 -->
    <user-stats-panel
      :user-stats="userStats"
      @handleSetLineChartData="handleSetLineChartData"
    />

    <!-- 用户增长趋势图 -->
    <el-row style="background: #fff; padding: 16px 16px 0; margin-bottom: 32px">
      <user-growth-chart :chart-data="userGrowthData" />
    </el-row>

    <!-- 二维码统计趋势图 -->
    <el-row style="background: #fff; padding: 16px 16px 0; margin-bottom: 32px">
      <qr-trend-chart :chart-data="qrTrendData" />
    </el-row>

    <!-- 最近活动和问卷统计 -->
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12">
        <recent-activities
          :activities="recentActivities"
          :loading="activitiesLoading"
          @view-detail="handleViewActivityDetail"
          @view-all="handleViewAllActivities"
        />
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
        <survey-stats
          :survey-stats="surveyStats"
          :recent-surveys="recentSurveys"
          :loading="surveyLoading"
          @view-survey="handleViewSurvey"
          @analyze-survey="handleAnalyzeSurvey"
          @view-all="handleViewAllSurveys"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import UserStatsPanel from './components/UserStatsPanel'
import UserGrowthChart from './components/UserGrowthChart'
import QrTrendChart from './components/QrTrendChart'
import RecentActivities from './components/RecentActivities'
import SurveyStats from './components/SurveyStats'
import {
  getUserStats,
  getRecentActivities,
  getQrCodeTrends,
  getSurveyStats,
  getRecentSurveys,
  getUserGrowthTrend
} from '@/api/dashboard'

export default {
  name: 'DashboardAdmin',
  components: {
    UserStatsPanel,
    UserGrowthChart,
    QrTrendChart,
    RecentActivities,
    SurveyStats
  },
  data() {
    return {
      userStats: {
        totalUsers: 0,
        todayNewUsers: 0,
        todayQrScans: 0,
        activeSurveys: 0
      },
      userGrowthData: {
        newUsers: [],
        totalUsers: [],
        dates: []
      },
      qrTrendData: {
        expectedData: [],
        actualData: [],
        dates: []
      },
      recentActivities: [],
      activitiesLoading: false,
      surveyStats: {
        totalSurveys: 0,
        activeSurveys: 0,
        completedSurveys: 0,
        totalResponses: 0
      },
      recentSurveys: [],
      surveyLoading: false
    }
  },
  created() {
    this.fetchDashboardData()
  },
  methods: {
    async fetchDashboardData() {
      try {
        await Promise.all([
          this.fetchUserStats(),
          this.fetchUserGrowthTrend(),
          this.fetchQrTrends(),
          this.fetchRecentActivities(),
          this.fetchSurveyData()
        ])
      } catch (error) {
        console.error('获取仪表板数据失败:', error)
        this.$message.error('获取数据失败，请刷新页面重试')
      }
    },

    async fetchUserStats() {
      try {
        const response = await getUserStats()
        if (response.code === 200) {
          this.userStats = response.data
        }
      } catch (error) {
        // 使用模拟数据
        this.userStats = {
          totalUsers: 15420,
          todayNewUsers: 128,
          todayQrScans: 856,
          activeSurveys: 12
        }
      }
    },

    async fetchUserGrowthTrend() {
      try {
        const response = await getUserGrowthTrend({ days: 30 })
        if (response.code === 200) {
          this.userGrowthData = response.data
        }
      } catch (error) {
        // 使用模拟数据
        const dates = []
        const newUsers = []
        const totalUsers = []
        let total = 15000

        for (let i = 29; i >= 0; i--) {
          const date = new Date()
          date.setDate(date.getDate() - i)
          dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))

          const newUser = Math.floor(Math.random() * 200) + 50
          newUsers.push(newUser)
          total += newUser
          totalUsers.push(total)
        }

        this.userGrowthData = { dates, newUsers, totalUsers }
      }
    },

    async fetchQrTrends() {
      try {
        const response = await getQrCodeTrends({ days: 7 })
        if (response.code === 200) {
          this.qrTrendData = response.data
        }
      } catch (error) {
        // 使用模拟数据
        const dates = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        const expectedData = [820, 932, 901, 934, 1290, 1330, 1320]
        const actualData = [750, 890, 950, 980, 1200, 1400, 1350]

        this.qrTrendData = { dates, expectedData, actualData }
      }
    },

    async fetchRecentActivities() {
      this.activitiesLoading = true
      try {
        const response = await getRecentActivities({ limit: 10 })
        if (response.code === 200) {
          this.recentActivities = response.data
        }
      } catch (error) {
        // 使用模拟数据
        this.recentActivities = [
          {
            id: 1,
            type: 'user_register',
            title: '新用户注册',
            description: '用户"张三"完成注册',
            createTime: new Date(Date.now() - 5 * 60 * 1000),
            status: 'active'
          },
          {
            id: 2,
            type: 'qr_scan',
            title: '二维码扫描',
            description: '产品推广二维码被扫描',
            createTime: new Date(Date.now() - 15 * 60 * 1000),
            status: 'completed'
          },
          {
            id: 3,
            type: 'survey_submit',
            title: '问卷提交',
            description: '用户完成"客户满意度调查"',
            createTime: new Date(Date.now() - 30 * 60 * 1000),
            status: 'completed'
          },
          {
            id: 4,
            type: 'activity_join',
            title: '活动参与',
            description: '用户参与"春季促销活动"',
            createTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
            status: 'active'
          }
        ]
      } finally {
        this.activitiesLoading = false
      }
    },

    async fetchSurveyData() {
      this.surveyLoading = true
      try {
        const [statsResponse, surveysResponse] = await Promise.all([
          getSurveyStats(),
          getRecentSurveys({ limit: 5 })
        ])

        if (statsResponse.code === 200) {
          this.surveyStats = statsResponse.data
        }

        if (surveysResponse.code === 200) {
          this.recentSurveys = surveysResponse.data
        }
      } catch (error) {
        // 使用模拟数据
        this.surveyStats = {
          totalSurveys: 45,
          activeSurveys: 12,
          completedSurveys: 28,
          totalResponses: 3420
        }

        this.recentSurveys = [
          {
            id: 1,
            title: '客户满意度调查',
            createTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
            responseCount: 156,
            status: 'active'
          },
          {
            id: 2,
            title: '产品使用体验问卷',
            createTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            responseCount: 89,
            status: 'active'
          },
          {
            id: 3,
            title: '员工培训反馈',
            createTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            responseCount: 234,
            status: 'completed'
          }
        ]
      } finally {
        this.surveyLoading = false
      }
    },

    handleSetLineChartData(type) {
      // 根据类型切换图表数据
      switch (type) {
        case 'users':
          this.fetchUserGrowthTrend()
          break
        case 'newUsers':
          this.fetchUserGrowthTrend()
          break
        case 'qrScans':
          this.fetchQrTrends()
          break
        case 'surveys':
          this.fetchSurveyData()
          break
      }
    },

    handleViewActivityDetail(activity) {
      // 跳转到活动详情页
      this.$router.push(`/activity/detail/${activity.id}`)
    },

    handleViewAllActivities() {
      // 跳转到活动列表页
      this.$router.push('/activity/list')
    },

    handleViewSurvey(survey) {
      // 跳转到问卷详情页
      this.$router.push(`/survey/detail/${survey.id}`)
    },

    handleAnalyzeSurvey(survey) {
      // 跳转到问卷分析页
      this.$router.push(`/survey/analysis/${survey.id}`)
    },

    handleViewAllSurveys() {
      // 跳转到问卷列表页
      this.$router.push('/survey/list')
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .github-corner {
    position: absolute;
    top: 0px;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
