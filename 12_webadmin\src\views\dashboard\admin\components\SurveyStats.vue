<template>
  <div class="survey-stats">
    <div class="stats-header">
      <h3>问卷统计</h3>
      <el-button type="text" @click="viewAll">查看全部</el-button>
    </div>
    
    <div class="stats-overview" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ surveyStats.totalSurveys || 0 }}</div>
            <div class="stat-label">总问卷数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ surveyStats.activeSurveys || 0 }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ surveyStats.completedSurveys || 0 }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ surveyStats.totalResponses || 0 }}</div>
            <div class="stat-label">总回答数</div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <div class="recent-surveys">
      <h4>最近问卷</h4>
      <div class="survey-list">
        <div 
          v-for="survey in recentSurveys" 
          :key="survey.id" 
          class="survey-item"
          @click="viewSurvey(survey)"
        >
          <div class="survey-info">
            <div class="survey-title">{{ survey.title }}</div>
            <div class="survey-meta">
              <span class="survey-date">{{ formatDate(survey.createTime) }}</span>
              <span class="survey-responses">{{ survey.responseCount || 0 }}人参与</span>
            </div>
          </div>
          <div class="survey-status">
            <el-tag :type="getSurveyStatusType(survey.status)" size="mini">
              {{ getSurveyStatusText(survey.status) }}
            </el-tag>
          </div>
          <div class="survey-action">
            <el-button type="text" size="mini" @click.stop="analyzeSurvey(survey)">
              分析
            </el-button>
          </div>
        </div>
        
        <div v-if="recentSurveys.length === 0" class="no-data">
          <p>暂无问卷数据</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SurveyStats',
  props: {
    surveyStats: {
      type: Object,
      default: () => ({
        totalSurveys: 0,
        activeSurveys: 0,
        completedSurveys: 0,
        totalResponses: 0
      })
    },
    recentSurveys: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getSurveyStatusType(status) {
      const typeMap = {
        'active': 'success',
        'draft': 'info',
        'completed': 'warning',
        'closed': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    getSurveyStatusText(status) {
      const textMap = {
        'active': '进行中',
        'draft': '草稿',
        'completed': '已完成',
        'closed': '已关闭'
      }
      return textMap[status] || '未知'
    },
    
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },
    
    viewSurvey(survey) {
      this.$emit('view-survey', survey)
    },
    
    analyzeSurvey(survey) {
      this.$emit('analyze-survey', survey)
    },
    
    viewAll() {
      this.$emit('view-all')
    }
  }
}
</script>

<style lang="scss" scoped>
.survey-stats {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .stats-overview {
    margin-bottom: 30px;
    
    .stat-item {
      text-align: center;
      padding: 20px 0;
      border-radius: 4px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      
      &:nth-child(2) {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &:nth-child(3) {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &:nth-child(4) {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
      
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 8px;
      }
      
      .stat-label {
        font-size: 14px;
        opacity: 0.9;
      }
    }
  }
  
  .recent-surveys {
    h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }
    
    .survey-list {
      max-height: 300px;
      overflow-y: auto;
      
      .survey-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.3s;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        .survey-info {
          flex: 1;
          
          .survey-title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .survey-meta {
            font-size: 12px;
            color: #909399;
            
            .survey-date {
              margin-right: 12px;
            }
          }
        }
        
        .survey-status {
          margin: 0 12px;
        }
        
        .survey-action {
          margin-left: 12px;
        }
      }
      
      .no-data {
        text-align: center;
        padding: 40px 0;
        color: #909399;
        
        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
